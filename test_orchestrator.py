#!/usr/bin/env python3
"""
Test script for the Orchestrator Agent
"""

import asyncio
import sys
from pathlib import Path

# Add orchestrator_agent to path
orchestrator_path = Path(__file__).parent / "orchestrator_agent"
sys.path.insert(0, str(orchestrator_path))

async def test_orchestrator():
    """Test the orchestrator agent with sample questions"""
    
    print("🎭 Testing Orchestrator Agent")
    print("=" * 50)
    
    try:
        # Import the orchestrator agent
        from agent import root_agent
        print("✅ Orchestrator agent imported successfully")
        print(f"Agent name: {root_agent.name}")
        print(f"Agent description: {root_agent.description}")
        print(f"Number of tools: {len(root_agent.tools) if root_agent.tools else 0}")
        
        if root_agent.tools:
            print("Available tools:")
            for tool in root_agent.tools:
                print(f"  - {tool.name}: {tool.description}")
        
        print("\n" + "=" * 50)
        print("🧪 Testing with sample questions")
        print("=" * 50)
        
        # Test questions
        test_questions = [
            "¿Qué es Dropi?",
            "¿Cuántas órdenes están pendientes?",
            "¿Cómo funciona Dropi Academy?",
            "¿Cuáles son las estadísticas de órdenes?"
        ]
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n{i}. Testing: {question}")
            print("-" * 30)
            
            try:
                # Create a simple runner to test the agent
                from google.adk.runners import Runner
                from google.adk.sessions.in_memory_session_service import InMemorySessionService
                from google.adk.memory.in_memory_memory_service import InMemoryMemoryService
                from google.genai import types
                
                runner = Runner(
                    app_name="orchestrator_test",
                    agent=root_agent,
                    session_service=InMemorySessionService(),
                    memory_service=InMemoryMemoryService()
                )
                
                # Create a session
                session = await runner.session_service.create_session(
                    app_name="orchestrator_test",
                    user_id="test_user"
                )
                
                # Create the message
                content = types.Content(
                    role="user",
                    parts=[types.Part.from_text(text=question)]
                )
                
                # Run the agent
                response_parts = []
                async for event in runner.run_async(
                    user_id=session.user_id,
                    session_id=session.id,
                    new_message=content
                ):
                    if event.content and event.content.parts:
                        for part in event.content.parts:
                            if part.text:
                                response_parts.append(part.text)
                
                response = ''.join(response_parts)
                print(f"✅ Response: {response[:200]}{'...' if len(response) > 200 else ''}")
                
            except Exception as e:
                print(f"❌ Error testing question: {e}")
                import traceback
                traceback.print_exc()
        
    except Exception as e:
        print(f"❌ Failed to import or test orchestrator: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_orchestrator())
    if success:
        print("\n🎉 Orchestrator test completed!")
    else:
        print("\n💥 Orchestrator test failed!")
        sys.exit(1)
