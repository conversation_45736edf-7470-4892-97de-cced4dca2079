#!/usr/bin/env python3
"""
Orders Agent Web Interface - Simple Version

Versión simplificada del agente de órdenes para pruebas.
"""

from google.adk.agents import Agent

# Crear un agente básico de órdenes
agent = Agent(
    model="gemini-2.0-flash",
    name="orders_agent_simple",
    description="Agente especializado en gestión de órdenes",
    instruction="""
    Eres un asistente especializado en GESTIÓN DE ÓRDENES. 

    Puedes ayudar con:
    - Consultas sobre órdenes pendientes
    - Búsqueda de órdenes por fecha
    - Información de clientes
    - Estadísticas de órdenes

    Responde siempre en español y mantén un tono profesional.
    
    Nota: Actualmente estoy en modo de prueba. Para funcionalidad completa, 
    necesito acceso a la base de datos de órdenes.
    """
)

__all__ = ["agent"]
