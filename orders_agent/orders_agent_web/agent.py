#!/usr/bin/env python3
"""
Orders Agent Web Interface

Agente de órdenes para la interfaz web de ADK.
Proporciona capacidades de gestión de órdenes a través de la interfaz web.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Agregar el directorio src al path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

# Cargar variables de entorno
env_file = Path(__file__).parent.parent.parent / "qa_agent" / ".env"
if env_file.exists():
    try:
        from dotenv import load_dotenv
        load_dotenv(env_file)
    except ImportError:
        logger.warning("python-dotenv no está disponible. Usando variables de entorno del sistema.")
        # Cargar manualmente las variables más importantes
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ.setdefault(key.strip(), value.strip())

# Importar y crear el agente
try:
    from agent import create_orders_agent, ensure_orders_initialized

    # Crear agente especializado en órdenes
    orders_agent = create_orders_agent()

    # Inicializar el sistema de órdenes al cargar el módulo
    try:
        # Ejecutar la inicialización de manera síncrona
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(ensure_orders_initialized())
        loop.close()
        logger.info("✅ Sistema de órdenes inicializado correctamente")
    except Exception as e:
        logger.warning(f"⚠️  Advertencia: No se pudo inicializar el sistema de órdenes: {e}")
        logger.warning("   El agente funcionará sin capacidades completas de órdenes.")

    # Hacer el agente disponible para el ADK web server
    agent = orders_agent

except Exception as e:
    logger.error(f"❌ Error cargando el agente de órdenes: {e}")
    # Fallback a un agente básico
    from google.adk.agents import Agent
    agent = Agent(
        model="gemini-2.0-flash",
        name="orders_fallback_agent",
        description="Agente básico de fallback para órdenes",
        instruction="Solo puedo responder preguntas sobre órdenes. Por favor, pregúntame algo relacionado con órdenes."
    )
    logger.info("🔄 Agente de fallback para órdenes cargado")

logger.info("🤖 Agente de órdenes web cargado correctamente")
