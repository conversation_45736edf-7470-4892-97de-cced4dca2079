# Configuración del Orders Agent
# Copia este archivo a .env y configura las variables según tu entorno
# NOTA: Este agente utiliza la misma configuración de base de datos que qa_agent

# === Configuración de Autenticación ===

# Opción 1: API Key de Google (para Gemini)
# Obtén tu API key en: https://aistudio.google.com/app/apikey
GOOGLE_API_KEY=your_google_api_key_here

# === Configuración del Modelo ===

# Modelo por defecto a usar
DEFAULT_MODEL=gemini-2.0-flash

# === Configuración de la Aplicación ===

# Nombre de la aplicación
APP_NAME=orders_agent_app

# Nivel de logging
LOG_LEVEL=INFO

# === Configuración del Agente ===

# Temperatura del modelo (0.0 - 1.0)
TEMPERATURE=0.3

# Máximo número de tokens en la respuesta
MAX_OUTPUT_TOKENS=2048

# === Configuración de Base de Datos ===
# NOTA: Utiliza la misma configuración que qa_agent

# Database Configuration (shared with qa_agent)
DB_HOST=your_db_host
DB_NAME=your_db_name
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_PORT=5432
DATABASE_URL=postgresql+asyncpg://user:password@host:port/database
