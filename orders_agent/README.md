# Orders Agent - Agente de Gestión de Órdenes

Agente especializado en gestión y consulta de órdenes utilizando ADK (Agent Development Kit) y PostgreSQL.

## Características

- **Consulta de órdenes por estado**: Buscar órdenes pendientes, completadas, canceladas, etc.
- **Búsqueda por fecha**: Consultar órdenes de hoy, ayer, esta semana, o fechas específicas
- **Búsqueda por cliente**: Encontrar todas las órdenes de un cliente específico
- **Estadísticas de órdenes**: Obtener resúmenes y métricas generales
- **Respuestas en lenguaje natural**: Interacción en español con respuestas claras y organizadas
- **Integración con ADK Web**: Interfaz web completa para interacción

## Estructura del Proyecto

```
orders_agent/
├── orders_agent_web/          # Agente para interfaz web ADK
│   ├── __init__.py
│   └── agent.py
├── src/                       # Implementación core del agente
│   ├── __init__.py
│   ├── agent.py              # Agente principal
│   ├── database.py           # Gestión de PostgreSQL
│   └── order_tools.py        # Herramientas de consulta
├── .env.example              # Plantilla de configuración
├── requirements.txt          # Dependencias
└── README.md                # Este archivo
```

## Configuración

### 1. Variables de Entorno

El agente utiliza la misma configuración de base de datos que `qa_agent`. Asegúrate de que el archivo `.env` en `qa_agent/` esté configurado correctamente.

### 2. Base de Datos

El agente utiliza PostgreSQL con el esquema `agente_v5`. Las tablas se crean automáticamente:

- `orders`: Información principal de órdenes
- `order_items`: Productos/items de cada orden

## Uso

### 1. Interfaz Web ADK

Para usar la interfaz web:

```bash
# Desde el directorio raíz del proyecto
adk web
```

Luego abre tu navegador en `http://localhost:8000` y selecciona el agente "orders_agent_web".

### 2. Ejemplos de Consultas

El agente puede responder preguntas como:

- "¿Cuántas órdenes están pendientes?"
- "¿Cuáles son las órdenes de hoy?"
- "Muéstrame las órdenes del cliente María García"
- "¿Cuál es el total de ingresos?"
- "¿Cuántas órdenes se completaron esta semana?"

## Capacidades del Agente

### Búsqueda por Estado
- Órdenes pendientes (`pending`)
- Órdenes completadas (`completed`)
- Órdenes canceladas (`cancelled`)
- Órdenes en procesamiento (`processing`)
- Órdenes enviadas (`shipped`)

### Búsqueda por Fecha
- Consultas relativas: "hoy", "ayer", "esta semana", "último mes"
- Fechas específicas: "2025-01-10"
- Rangos de fechas automáticos

### Información Detallada
Para cada orden, el agente proporciona:
- Número de orden
- Estado actual
- Información del cliente
- Fecha de orden y entrega
- Monto total y moneda
- Lista detallada de productos
- Notas adicionales

## Datos de Ejemplo

El agente incluye datos de ejemplo que se insertan automáticamente si no existen órdenes en la base de datos:

- Órdenes con diferentes estados
- Clientes variados
- Productos diversos
- Fechas recientes para pruebas

## Arquitectura Técnica

### Herramientas Disponibles

1. **OrderSearchTool**: Búsqueda por estado
2. **OrderDateSearchTool**: Búsqueda por fecha
3. **OrderCustomerSearchTool**: Búsqueda por cliente
4. **OrderStatisticsTool**: Estadísticas generales

### Base de Datos

- **PostgreSQL** con esquema `agente_v5`
- **Conexión asíncrona** con `asyncpg`
- **Índices optimizados** para consultas frecuentes
- **Transacciones seguras** para integridad de datos

### Integración ADK

- Compatible con **ADK Web Interface**
- Herramientas **BaseTool** personalizadas
- **Esquemas de entrada** validados
- **Respuestas JSON** estructuradas

## Limitaciones y Boundaries

El agente está diseñado para responder **únicamente** preguntas relacionadas con órdenes. Para preguntas fuera de este dominio, redirige al usuario apropiadamente.

## Desarrollo y Extensión

Para agregar nuevas funcionalidades:

1. **Nuevas consultas**: Agregar métodos en `database.py`
2. **Nuevas herramientas**: Crear clases en `order_tools.py`
3. **Nuevas capacidades**: Registrar herramientas en `agent.py`

## Troubleshooting

### Problemas Comunes

1. **Error de conexión a base de datos**: Verificar configuración en `qa_agent/.env`
2. **Agente no responde**: Verificar logs en la consola
3. **Datos no encontrados**: El agente insertará datos de ejemplo automáticamente

### Logs

El agente proporciona logs detallados para debugging:
- Inicialización de base de datos
- Ejecución de consultas
- Errores y advertencias

## Compatibilidad

- **Python 3.8+**
- **PostgreSQL 12+**
- **ADK Framework**
- **Gemini Models** (configurables)
