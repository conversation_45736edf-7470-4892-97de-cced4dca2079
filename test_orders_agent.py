#!/usr/bin/env python3
"""
Test script for Orders Agent
Tests database connection and tool functionality
"""

import asyncio
import os
import sys
import logging
from pathlib import Path

# Add the orders_agent src directory to the path
orders_agent_src = Path(__file__).parent / "orders_agent" / "src"
sys.path.insert(0, str(orders_agent_src))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_database_connection():
    """Test database connection and basic operations"""
    print("=== Testing Database Connection ===")
    
    # Check environment variables
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("❌ DATABASE_URL not set")
        return False
    
    print(f"✅ DATABASE_URL configured: {database_url[:50]}...")
    
    try:
        # Import database manager
        from database import orders_db_manager
        
        # Initialize database
        await orders_db_manager.initialize()
        print("✅ Database initialized successfully")
        
        # Check if we have sample data
        stats = await orders_db_manager.get_order_statistics()
        print(f"📊 Current statistics: {stats}")
        
        if stats['total_orders'] == 0:
            print("📝 Inserting sample data...")
            await orders_db_manager.insert_sample_orders()
            stats = await orders_db_manager.get_order_statistics()
            print(f"📊 Updated statistics: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

async def test_order_tools():
    """Test order query tools"""
    print("\n=== Testing Order Tools ===")
    
    try:
        from order_tools import (
            search_orders_by_status_function,
            search_orders_by_date_function,
            search_orders_by_customer_function,
            get_order_statistics_function
        )
        
        # Test 1: Search by status
        print("\n1. Testing search by status (pending)...")
        result = await search_orders_by_status_function("pending")
        print(f"Result: {result[:200]}...")
        
        # Test 2: Search by status (Spanish)
        print("\n2. Testing search by status (pendientes)...")
        result = await search_orders_by_status_function("pendientes")
        print(f"Result: {result[:200]}...")
        
        # Test 3: Search by date
        print("\n3. Testing search by date (today)...")
        result = await search_orders_by_date_function("hoy")
        print(f"Result: {result[:200]}...")
        
        # Test 4: Search by customer
        print("\n4. Testing search by customer...")
        result = await search_orders_by_customer_function("María")
        print(f"Result: {result[:200]}...")
        
        # Test 5: Get statistics
        print("\n5. Testing statistics...")
        result = await get_order_statistics_function()
        print(f"Result: {result[:200]}...")
        
        print("✅ All tool tests completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Tool testing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_agent_tools():
    """Test ADK agent tools"""
    print("\n=== Testing ADK Agent Tools ===")
    
    try:
        # Add orders_agent_web to path
        orders_agent_web_path = Path(__file__).parent / "orders_agent_web"
        sys.path.insert(0, str(orders_agent_web_path))
        
        from agent import OrderStatusSearchTool, DATABASE_AVAILABLE
        
        print(f"Database available: {DATABASE_AVAILABLE}")
        
        if DATABASE_AVAILABLE:
            # Test the ADK tool
            tool = OrderStatusSearchTool()
            result = await tool.run("pending")
            print(f"ADK Tool result: {result[:200]}...")
            print("✅ ADK tool test completed successfully")
        else:
            print("⚠️ Database not available for ADK tool testing")
        
        return True
        
    except Exception as e:
        print(f"❌ ADK tool testing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🚀 Starting Orders Agent Tests\n")
    
    # Test database connection
    db_success = await test_database_connection()
    
    if db_success:
        # Test order tools
        tools_success = await test_order_tools()
        
        if tools_success:
            # Test ADK agent tools
            agent_success = await test_agent_tools()
            
            if agent_success:
                print("\n🎉 All tests passed successfully!")
            else:
                print("\n❌ ADK agent tool tests failed")
        else:
            print("\n❌ Order tool tests failed")
    else:
        print("\n❌ Database connection tests failed")
    
    print("\n=== Test Summary ===")
    print("Next steps:")
    print("1. If database tests failed, check DATABASE_URL environment variable")
    print("2. If tool tests failed, check order_tools.py implementation")
    print("3. If ADK tool tests failed, check agent.py tool registration")

if __name__ == "__main__":
    asyncio.run(main())
