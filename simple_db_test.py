#!/usr/bin/env python3
"""
Simple database test for Orders Agent
"""

import asyncio
import os
import sys
from pathlib import Path

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Environment variables loaded")
except ImportError:
    print("⚠️ python-dotenv not available, using system environment")

# Check database URL
database_url = os.getenv("DATABASE_URL")
print(f"DATABASE_URL: {database_url}")

if not database_url:
    print("❌ DATABASE_URL not set")
    sys.exit(1)

async def test_connection():
    """Test basic database connection"""
    try:
        import asyncpg
        print("✅ asyncpg imported successfully")
        
        # Test connection
        conn = await asyncpg.connect(database_url)
        print("✅ Database connection successful")
        
        # Test query
        result = await conn.fetchval("SELECT 1")
        print(f"✅ Test query result: {result}")
        
        await conn.close()
        print("✅ Connection closed")
        
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_connection())
