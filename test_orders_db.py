#!/usr/bin/env python3
"""
Test script for Orders Database
"""

import asyncio
import sys
from pathlib import Path

# Add the orders_agent src directory to path
sys.path.insert(0, str(Path(__file__).parent / "orders_agent" / "src"))

from database import orders_db_manager

async def test_and_setup():
    try:
        # Test basic connection
        await orders_db_manager.initialize()
        print('✅ Database connection successful')
        
        # Insert sample data if needed
        await orders_db_manager.insert_sample_orders()
        print('✅ Sample data inserted/verified')
        
        # Check if tables exist and have data
        total_orders = await orders_db_manager.get_total_orders()
        print(f'📊 Total orders in database: {total_orders}')
        
        # Get statistics
        stats = await orders_db_manager.get_order_statistics()
        print(f'📈 Order statistics: {stats}')
        
        # Try to get some sample orders
        orders = await orders_db_manager.get_recent_orders(limit=3)
        print(f'📋 Recent orders: {len(orders)} found')
        for order in orders:
            print(f'  - Order {order.get("order_number", "N/A")}: {order.get("status", "N/A")} - ${order.get("total_amount", 0)}')
            
    except Exception as e:
        print(f'❌ Database error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_and_setup())
