#!/usr/bin/env python3
"""
Test agent import and basic functionality
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Environment variables loaded")
except ImportError:
    print("⚠️ python-dotenv not available, using system environment")

# Set DATABASE_URL if not set
if not os.getenv("DATABASE_URL"):
    os.environ["DATABASE_URL"] = "postgresql+asyncpg://postgres:<EMAIL>:5432/postgres"
    print("✅ DATABASE_URL set from hardcoded value")

print(f"DATABASE_URL: {os.getenv('DATABASE_URL')[:50]}...")

# Test importing the agent
try:
    print("🔍 Attempting to import orders_agent_web.agent...")
    
    # Add orders_agent_web to path
    orders_agent_web_path = current_dir / "orders_agent_web"
    sys.path.insert(0, str(orders_agent_web_path))
    
    # Import the agent module
    import agent
    print("✅ Agent module imported successfully")
    
    # Check if DATABASE_AVAILABLE
    if hasattr(agent, 'DATABASE_AVAILABLE'):
        print(f"📊 DATABASE_AVAILABLE: {agent.DATABASE_AVAILABLE}")
    
    # Check if root_agent exists
    if hasattr(agent, 'root_agent'):
        print(f"🤖 Root agent created: {agent.root_agent}")
        print(f"🔧 Agent tools: {len(agent.root_agent.tools) if hasattr(agent.root_agent, 'tools') else 'No tools attribute'}")
    
    print("✅ Agent import test completed successfully")
    
except Exception as e:
    print(f"❌ Agent import failed: {e}")
    import traceback
    traceback.print_exc()

print("\n=== Test Summary ===")
print("If the agent imported successfully, the tool execution issue might be elsewhere.")
print("Check the ADK web interface logs for more details about tool execution.")
