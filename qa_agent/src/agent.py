# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Agente Q&A con RAG usando Google ADK
"""

import os
import sys
from typing import List, Optional, Dict, Any
import asyncio
import logging
from pathlib import Path

from google.adk.agents import Agent
from google.adk.tools.base_tool import BaseTool
from google.genai import types
import json

# Importar módulos RAG
from database import db_manager
from embeddings import embedding_generator
from document_processor import document_processor

logger = logging.getLogger(__name__)


async def search_documents_function(query: str) -> str:
    """
    Función para búsqueda semántica en la documentación de Dropi

    Args:
        query: Consulta del usuario sobre Dropi

    Returns:
        Contexto relevante encontrado en la documentación de Dropi
    """
    try:
        logger.info(f"🔍 Buscando información sobre: {query}")

        # Generar embedding de la consulta
        query_embedding = await embedding_generator.generate_embedding(query)

        # Buscar chunks similares
        similar_chunks = await db_manager.search_similar_chunks(
            query_embedding=query_embedding,
            limit=5,
            similarity_threshold=0.4
        )

        if not similar_chunks:
            return "No se encontró información sobre eso en la documentación de Dropi disponible."

        # Formatear contexto de manera más natural
        context_parts = []
        for chunk in similar_chunks:
            context_parts.append(
                f"📄 Documento: {chunk['filename']} (similitud: {chunk['similarity']:.2f})\n"
                f"{chunk['content']}"
            )

        context = "\n\n".join(context_parts)

        logger.info(f"✅ Encontrados {len(similar_chunks)} chunks relevantes sobre Dropi")
        return context

    except Exception as e:
        logger.error(f"❌ Error en búsqueda RAG: {e}")
        return "Error al buscar información en la documentación de Dropi."


class RAGSearchTool(BaseTool):
    """Herramienta para búsqueda semántica en documentos de Dropi"""

    def __init__(self):
        super().__init__(
            name="search_documents",
            description="Busca información relevante sobre Dropi en la documentación oficial"
        )

    def get_parameters_schema(self):
        """Retorna el esquema de parámetros para la herramienta"""
        return {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Consulta para buscar información sobre Dropi en los documentos"
                }
            },
            "required": ["query"]
        }

    async def run(self, query: str) -> str:
        """Ejecutar búsqueda usando la función auxiliar"""
        return await search_documents_function(query)


def create_rag_agent(model_name: Optional[str] = None) -> Agent:
    """
    Crear un agente Q&A con capacidades RAG

    Args:
        model_name: Nombre del modelo a usar. Por defecto usa gemini-2.0-flash.

    Returns:
        Agent configurado para Q&A con RAG.
    """

    # Configurar modelo por defecto
    if model_name is None:
        model_name = "gemini-2.0-flash"

    # Usar modelo directo de ADK
    model = model_name

    # Crear agente de Q&A con RAG especializado en Dropi (sin herramientas por ahora)
    rag_agent = Agent(
        model=model,
        name="dropi_qa_agent",
        description="Agente especializado en responder preguntas sobre Dropi basándose únicamente en la documentación oficial de Dropi.",
        instruction="""
        Eres un asistente especializado en DROPI. Responde ÚNICAMENTE preguntas sobre Dropi usando la documentación oficial.

        PROCESO:
        1. Si la pregunta es sobre Dropi, busca información en la documentación
        2. Si la pregunta NO es sobre Dropi, responde: "Solo puedo responder preguntas sobre Dropi. Por favor, pregúntame algo relacionado con Dropi."
        3. Si no encuentras información relevante, responde: "No tengo información sobre eso en la documentación de Dropi disponible."

        REGLAS:
        - NUNCA inventes información
        - Responde de forma natural y conversacional
        - Menciona que la información proviene de la documentación oficial de Dropi
        - NO generes código ni ejemplos técnicos a menos que estén en los documentos
        """,
        generate_content_config=types.GenerateContentConfig(
            temperature=0.3,  # Temperatura baja para respuestas más deterministas
            max_output_tokens=1024,
            safety_settings=[
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_HARASSMENT,
                    threshold=types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                    threshold=types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                    threshold=types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                    threshold=types.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                ),
            ]
        ),
    )

    return rag_agent


async def initialize_rag_system():
    """Inicializar el sistema RAG"""
    try:
        # Inicializar base de datos
        await db_manager.initialize()
        logger.info("Sistema RAG inicializado correctamente")
        return True
    except Exception as e:
        logger.error(f"Error inicializando sistema RAG: {e}")
        return False


# Crear instancia del agente RAG
root_agent = create_rag_agent()

# Variable para controlar si el sistema RAG está inicializado
_rag_initialized = False


async def ensure_rag_initialized():
    """Asegurar que el sistema RAG esté inicializado"""
    global _rag_initialized
    if not _rag_initialized:
        success = await initialize_rag_system()
        if success:
            _rag_initialized = True
        return success
    return True
