#!/usr/bin/env python3
"""
Test script for Order Tools
"""

import asyncio
import sys
from pathlib import Path

# Add the orders_agent src directory to path
sys.path.insert(0, str(Path(__file__).parent / "orders_agent" / "src"))

from order_tools import (
    search_orders_by_status_function,
    get_order_statistics_function,
    search_orders_by_customer_function
)

async def test_order_tools():
    try:
        print("🧪 Testing Order Tools...")
        
        # Test 1: Search orders by status
        print("\n1. Testing search by status (pending):")
        result1 = await search_orders_by_status_function("pending")
        print(f"Result: {result1[:200]}...")
        
        # Test 2: Search orders by status (completed)
        print("\n2. Testing search by status (completed):")
        result2 = await search_orders_by_status_function("completed")
        print(f"Result: {result2[:200]}...")
        
        # Test 3: Get order statistics
        print("\n3. Testing order statistics:")
        result3 = await get_order_statistics_function()
        print(f"Result: {result3[:200]}...")
        
        # Test 4: Search by customer
        print("\n4. Testing search by customer:")
        result4 = await search_orders_by_customer_function("María")
        print(f"Result: {result4[:200]}...")
        
        print("\n✅ All order tools tested successfully!")
        
    except Exception as e:
        print(f"❌ Error testing order tools: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_order_tools())
