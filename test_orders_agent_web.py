#!/usr/bin/env python3
"""
Test script for Orders Agent Web
"""

import asyncio
import sys
from pathlib import Path

# Add the orders_agent_web directory to path
sys.path.insert(0, str(Path(__file__).parent / "orders_agent_web"))

async def test_database_connection():
    """Test database connection and <PERSON> orders"""
    try:
        from database import orders_db_manager
        
        print("🔄 Testing database connection...")
        
        # Test basic connection
        await orders_db_manager.initialize()
        print('✅ Database connection successful')
        
        # Insert sample data if needed
        await orders_db_manager.insert_sample_orders()
        print('✅ Sample data inserted/verified')
        
        # Get statistics
        stats = await orders_db_manager.get_order_statistics()
        print(f'📈 Order statistics: {stats}')
        
        # Test <PERSON> orders specifically
        print("\n🔍 Testing Carlos Rodríguez orders...")
        carlos_orders = await orders_db_manager.get_orders_by_customer("<PERSON>")
        print(f'📋 Found {len(carlos_orders)} orders for <PERSON>')
        
        for order in carlos_orders:
            print(f'  - Order {order["order_number"]}: {order["status"]} - ${order["total_amount"]}')
            print(f'    Date: {order["order_date"]}, Items: {len(order["items"])}')
            for item in order["items"]:
                print(f'      * {item["product_name"]} x{item["quantity"]} = ${item["total_price"]}')
        
        await orders_db_manager.close()
        return True
        
    except Exception as e:
        print(f'❌ Database error: {e}')
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests"""
    print("🚀 Starting Orders Agent Web Tests\n")
    
    # Test database
    db_success = await test_database_connection()
    
    print(f"\n📊 Test Results:")
    print(f"  Database: {'✅ PASS' if db_success else '❌ FAIL'}")
    
    if db_success:
        print("\n🎉 Database test passed! Orders agent is ready.")
    else:
        print("\n⚠️ Database test failed. Check the errors above.")

if __name__ == "__main__":
    asyncio.run(main())
