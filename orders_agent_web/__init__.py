#!/usr/bin/env python3
"""
Orders Agent Web Interface

Módulo para integración con ADK web interface.
Expone el agente de órdenes para uso en la interfaz web.
"""

import sys
from pathlib import Path

# Agregar el directorio src del orders_agent al path
src_path = Path(__file__).parent.parent / "orders_agent" / "src"
sys.path.insert(0, str(src_path))

from agent import root_agent

# Hacer el agente disponible para el ADK web server
agent = root_agent

__all__ = ["agent"]
